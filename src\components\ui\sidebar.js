"use client";


import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import { 
  Settings, 
  Package, 
  Cpu, 
  FileText, 
  Menu
} from "lucide-react";

const sidebarItems = [
  {
    title: "Equipment",
    href: "/equipment",
    icon: Package,
  },
  {
    title: "Component",
    href: "/component", 
    icon: Cpu,
  },
  {
    title: "XYZ",
    href: "/xyz",
    icon: Settings,
  },
  {
    title: "Form",
    href: "/form-selection",
    icon: FileText,
  },
];

export function Sidebar({ className }) {
  const pathname = usePathname();


  return (
    <div className={cn(
      "fixed left-0 top-0 z-40 h-screen transition-all duration-300",
      "w-64",
      className
    )}>
      <div className="flex h-full flex-col bg-white border-r border-gray-200 shadow-lg">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 gradient-primary rounded-lg flex items-center justify-center">
              <Package className="w-5 h-5 text-white" />
            </div>
            <span className="font-bold text-xl bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Conveyor Belt
            </span>
          </div>
        </div>

        {/* Navigation */}
        <nav className="flex-1 p-4 space-y-2">
          {sidebarItems.map((item) => {
            const isActive = pathname === item.href || 
              (item.href === "/form-selection" && pathname === "/form");
            
            return (
              <Link
                key={item.href}
                href={item.href}
                className={cn(
                  "flex items-center space-x-3 px-3 py-3 rounded-lg transition-all duration-200 group",
                  isActive
                    ? "bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg"
                    : "text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                )}
              >
                <item.icon className={cn(
                  "w-5 h-5 transition-colors",
                  isActive ? "text-white" : "text-gray-500 group-hover:text-gray-700"
                )} />
                  <span className="font-medium">{item.title}</span>
              </Link>
            );
          })}
        </nav>

        {/* Footer */}
        <div className="p-4 border-t border-gray-200">
          <div className="text-xs text-gray-500 text-center">
            <p>Conveyor Belt v1.0</p>
            <p className="mt-1">Internal Tool</p>
          </div>
        </div>
      </div>
    </div>
  );
}
