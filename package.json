{"name": "conveyor-belt", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:seed": "node prisma/seed.js", "db:studio": "prisma studio"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@prisma/client": "^6.11.0", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toast": "^1.2.14", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "jose": "^6.0.11", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.525.0", "next": "15.3.4", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "prisma": "^6.11.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.59.0", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "track-dependancy-changes": "^1.0.2", "zod": "^3.25.71"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "tw-animate-css": "^1.3.4"}}