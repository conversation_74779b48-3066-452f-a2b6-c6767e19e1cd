export const formFields = [
  {
    id: "first_name",
    label: "First Name",
    type: "text",
    required: true,
    placeholder: "Enter first name",
  },
  {
    id: "last_name",
    label: "Last Name",
    type: "text",
    required: true,
    placeholder: "Enter last name",
  },
  {
    id: "email",
    label: "Email",
    type: "email",
    required: true,
    placeholder: "Enter email address",
  },
  {
    id: "password",
    label: "Password",
    type: "password",
    required: true,
    minLength: 6,
    placeholder: "Enter password",
  },
  {
    id: "phone",
    label: "Phone Number",
    type: "text",
    required: true,
    placeholder: "Enter phone number",
  },
  {
    id: "username",
    label: "Username",
    type: "text",
    required: true,
    placeholder: "Choose a username",
  },
  {
    id: "age",
    label: "Age",
    type: "number",
    required: false,
    min: 0,
    max: 120,
    placeholder: "Enter your age",
  },
  { id: "dob", label: "Date of Birth", type: "date", required: true },
  {
    id: "country",
    label: "Country",
    type: "dropdown",
    required: true,
    options: [
      { value: "india", label: "India" },
      { value: "usa", label: "USA" },
      { value: "germany", label: "Germany" },
    ],
  },
  {
    id: "city",
    label: "City",
    type: "text",
    required: false,
    placeholder: "Enter city name",
  },
  {
    id: "state",
    label: "State",
    type: "text",
    required: false,
    placeholder: "Enter state name",
  },
  {
    id: "zip",
    label: "Zip Code",
    type: "text",
    required: false,
    placeholder: "Enter zip code",
  },
  {
    id: "address",
    label: "Address",
    type: "textarea",
    required: false,
    placeholder: "Enter full address",
  },
  {
    id: "bio",
    label: "Bio",
    type: "textarea",
    required: false,
    maxLength: 300,
    placeholder: "Write about yourself",
  },
  {
    id: "profile_pic",
    label: "Profile Picture",
    type: "file",
    required: false,
  },
  { id: "resume", label: "Upload Resume", type: "file", required: false },
  {
    id: "website",
    label: "Website",
    type: "text",
    required: false,
    placeholder: "https://example.com",
  },
  { id: "linkedin", label: "LinkedIn URL", type: "text", required: false },
  { id: "github", label: "GitHub Username", type: "text", required: false },
  {
    id: "education_level",
    label: "Education Level",
    type: "dropdown",
    required: true,
    options: [
      { value: "high_school", label: "High School" },
      { value: "bachelor", label: "Bachelor's" },
      { value: "master", label: "Master's" },
      { value: "phd", label: "Ph.D" },
    ],
  },
  {
    id: "field_of_study",
    label: "Field of Study",
    type: "text",
    required: false,
  },
  {
    id: "experience",
    label: "Years of Experience",
    type: "number",
    required: false,
  },
  {
    id: "skills",
    label: "Key Skills",
    type: "textarea",
    required: false,
    placeholder: "E.g. React, Node.js, SQL",
  },
  {
    id: "expected_salary",
    label: "Expected Salary",
    type: "number",
    required: false,
    placeholder: "In USD",
  },
  {
    id: "availability_date",
    label: "Availability Date",
    type: "date",
    required: false,
  },
];
