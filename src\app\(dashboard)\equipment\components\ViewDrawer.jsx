"use client";

import {
  She<PERSON><PERSON>eader,
  SheetTitle,
  SheetDescription,
} from "@/components/ui/sheet";
import LeftDrawer from "@/components/common/LeftDrawer";
import { Badge } from "@/components/ui/badge";

export default function ViewDrawer({ open, onClose, data }) {
  if (!data) return null;

  return (
    <LeftDrawer open={open} onClose={onClose}>
      <SheetHeader>
        <SheetTitle>Equipment Details</SheetTitle>
        <SheetDescription>
          View the complete information for this equipment.
        </SheetDescription>
      </SheetHeader>

      <div className="space-y-6 py-6">
        {/* Equipment Name */}
        <div className="space-y-2">
          <h3 className="text-sm font-medium text-gray-900">Equipment Name</h3>
          <p className="text-lg font-semibold text-gray-800">{data.name}</p>
        </div>

        {/* Components */}
        <div className="space-y-3">
          <h3 className="text-sm font-medium text-gray-900">Components</h3>
          <div className="flex flex-wrap gap-2">
            {data.components?.map((component, index) => (
              <Badge
                key={index}
                variant="secondary"
                className="px-3 py-1 text-sm"
              >
                {component}
              </Badge>
            ))}
          </div>
          {(!data.components || data.components.length === 0) && (
            <p className="text-sm text-gray-500 italic">No components listed</p>
          )}
        </div>

        {/* Additional Info */}
        <div className="space-y-2">
          <h3 className="text-sm font-medium text-gray-900">Summary</h3>
          <div className="bg-gray-50 rounded-lg p-4 space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Total Components:</span>
              <span className="font-medium">
                {data.components?.length || 0}
              </span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Equipment ID:</span>
              <span className="font-medium">#{data.id}</span>
            </div>
          </div>
        </div>
      </div>
    </LeftDrawer>
  );
}
